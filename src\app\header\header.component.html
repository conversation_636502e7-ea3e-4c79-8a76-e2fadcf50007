<nav class="h-[70px] sticky top-0 z-50 border-b-1 border-pink-300 dark:border-dark-pink-border
  backdrop-blur-md bg-white/80 dark:bg-dark-bg-primary/80
  shadow-sm dark:shadow-lg dark:shadow-black/20
  lg:bg-white/70 lg:dark:bg-dark-bg-primary/70
  px-8 flex items-center justify-between transition-all duration-300">
  <!-- Menu toggle for mobile -->
  <input type="checkbox" id="check" class="hidden peer" />
  <label for="check" class="menu block lg:hidden sticky cursor-pointer z-50 text-gray-700 dark:text-gray-200">
    <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" fill="currentColor" class="bi bi-list transition-colors duration-300">
      <path fill-rule="evenodd"
        d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z" />
    </svg>
  </label>

  <!-- Logo -->
  <div class="logo">
    <h2 class="theme-text-primary font-bold cursor-pointer text-xl transition-colors duration-300" (click)="goTo(0); closeMobileMenu()">RA</h2>
  </div>

  <!-- Navigation Links -->
  <div
    class="nav-items peer-checked:right-0 fixed lg:static top-0 right-[-250px] h-screen lg:h-auto w-[250px] lg:w-auto flex flex-col lg:flex-row justify-evenly lg:justify-end items-start lg:items-center bg-red-400 dark:bg-dark-bg-secondary lg:bg-transparent dark:lg:bg-transparent transition-all duration-500 p-8 lg:p-0 gap-y-6 lg:gap-x-6">
    <ul class="flex flex-col lg:flex-row gap-y-4 lg:gap-x-4 theme-text-secondary dark:text-gray-200 text-[18px] font-medium">
      <li><a href="# " (click)="goTo(0); closeMobileMenu()" class="hover:text-pink-700 dark:hover:text-pink-400 relative after:block after:h-[3px] after:bg-pink-600 dark:after:bg-pink-400 after:w-0 hover:after:w-full after:transition-all after:duration-300 transition-colors duration-300">Home</a></li>
      <li><a href="#about" (click)="goTo(1); closeMobileMenu()" class="hover:text-pink-700 dark:hover:text-pink-400 relative after:block after:h-[3px] after:bg-pink-600 dark:after:bg-pink-400 after:w-0 hover:after:w-full after:transition-all after:duration-300 transition-colors duration-300">About</a></li>
      <li><a href="#projects" (click)="goTo(2); closeMobileMenu()" class="hover:text-pink-700 dark:hover:text-pink-400 relative after:block after:h-[3px] after:bg-pink-600 dark:after:bg-pink-400 after:w-0 hover:after:w-full after:transition-all after:duration-300 transition-colors duration-300">Projects</a></li>
      <li><a href="#skills" (click)="goTo(3); closeMobileMenu()" class="hover:text-pink-700 dark:hover:text-pink-400 relative after:block after:h-[3px] after:bg-pink-600 dark:after:bg-pink-400 after:w-0 hover:after:w-full after:transition-all after:duration-300 transition-colors duration-300">Skills</a></li>
      <li><a href="#experience" (click)="goTo(4); closeMobileMenu()" class="hover:text-pink-700 dark:hover:text-pink-400 relative after:block after:h-[3px] after:bg-pink-600 dark:after:bg-pink-400 after:w-0 hover:after:w-full after:transition-all after:duration-300 transition-colors duration-300">Experience</a></li>

      <li><a href="#contact" (click)="goTo(5); closeMobileMenu()" class="hover:text-pink-700 dark:hover:text-pink-400 relative after:block after:h-[3px] after:bg-pink-600 dark:after:bg-pink-400 after:w-0 hover:after:w-full after:transition-all after:duration-300 transition-colors duration-300">Contact</a></li>
    </ul>

    <!-- Dark Mode Toggle -->
    <div class="mt-4 lg:mt-0 lg:ml-4">
      <app-dark-mode-toggle></app-dark-mode-toggle>
    </div>
  </div>
</nav>
